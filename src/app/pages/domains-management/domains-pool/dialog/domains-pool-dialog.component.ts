import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute } from '@angular/router';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DOMAIN_TYPES, DomainPool, DomainPoolData, DomainType } from '../../../../common/models/domain.model';
import { ServerConfig } from '../../../../common/typings/server-config';
import { DomainsManagementService } from '../../domains-management.service';

export interface DomainsPoolDialogData {
  pool?: DomainPool;
  poolType: DomainType;
}

interface DomainPoolItem {
  code: string;
  displayName: string;
  type: string;
  selected: boolean;
  enabled: boolean;
  active: boolean;
}

const compareRow = (row: DomainPoolItem) => (item: DomainPoolItem): boolean => item.code === row.code;

@Component({
  selector: 'domains-pool-dialog',
  templateUrl: './domains-pool-dialog.component.html',
  styleUrls: ['./domains-pool-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DomainsPoolDialogComponent implements OnInit, OnDestroy {
  dataSource: MatTableDataSource<DomainPoolItem>;
  submitted = false;

  readonly poolType: DomainType;
  readonly title: string;
  readonly displayedColumns: string[];
  adapterOptions: SwuiSelectOption[];

  private readonly form: FormGroup;
  private readonly pool?: DomainPool;
  private readonly _destroyed$ = new Subject();

  constructor(
    @Inject(MAT_DIALOG_DATA) { pool, poolType }: DomainsPoolDialogData,
    fb: FormBuilder,
    private readonly dialogRef: MatDialogRef<DomainsPoolDialogComponent, DomainPoolData>,
    private readonly domainsService: DomainsManagementService,
    private cdr: ChangeDetectorRef,
    private readonly route: ActivatedRoute,
  ) {
    this.poolType = poolType;
    this.pool = pool;

    this.title = `DOMAINS.${(pool?.id ? 'edit' : 'add')}${poolType === DOMAIN_TYPES.dynamic ? 'Dynamic' : 'Static'}Pool`;
    this.displayedColumns = poolType === DOMAIN_TYPES.dynamic
      ? ['code', 'name', 'active']
      : ['code', 'name', 'active', 'type'];

    this.adapterOptions = [
      { id: '', text: 'None' }
    ];

    this.form = fb.group({
      name: [pool?.name ?? '', Validators.required],
      domainWatcherAdapterId: [pool?.domainWatcherAdapterId ?? '']
    });
  }

  ngOnInit(): void {
    // Load server config from route data to get domain watcher adapters
    const config: ServerConfig = this.route.snapshot.data.config;
    if (config?.domainWatcherAdapters) {
      this.adapterOptions = [
        { id: '', text: 'None' },
        ...config.domainWatcherAdapters.map((adapter: string) => ({
          id: adapter,
          text: adapter.charAt(0).toUpperCase() + adapter.slice(1)
        }))
      ];
      this.cdr.detectChanges();
    }

    this.domainsService.getList(this.poolType).pipe(takeUntil(this._destroyed$)).subscribe(domains => {
      const items: DomainPoolItem[] = [];
      for (const domain of domains) {
        const record = this.pool && this.pool.domains.find(({ id }) => domain.id === id);
        items.push({
          code: domain.id || '',
          displayName: domain.domain || '',
          type: domain.type || '',
          selected: Boolean(record),
          active: Boolean(record) && Boolean(record.isActive),
          enabled: true
        });
      }
      this.dataSource = new MatTableDataSource(items);
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  get selectedItems(): DomainPoolItem[] {
    return this.dataSource?.data.filter(item => item.selected) ?? [];
  }

  get nameControl(): FormControl {
    return this.form.get('name') as FormControl;
  }

  get domainWatcherAdapterIdControl(): FormControl {
    return this.form.get('domainWatcherAdapterId') as FormControl;
  }

  rowSelectedChanged(changeEvent: MatCheckboxChange, row: DomainPoolItem) {
    if (!changeEvent.checked) {
      const compareFn = compareRow(row);
      const record = this.dataSource.data.find(compareFn);
      if (record) {
        record.active = false;
      }
    }
  }

  rowActiveChanged(changeEvent: MatCheckboxChange, row: DomainPoolItem) {
    if (changeEvent.checked) {
      const compareFn = compareRow(row);
      const record = this.dataSource.data.find(compareFn);
      if (record) {
        record.selected = true;
      }
    }
  }

  submit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      const adapterValue = this.domainWatcherAdapterIdControl.value;
      this.dialogRef.close({
        name: this.nameControl.value,
        domainWatcherAdapterId: adapterValue || undefined,
        domains: this.selectedItems.map(item => ({
          id: item.code,
          isActive: item.active
        }))
      });
    }
  }
}
