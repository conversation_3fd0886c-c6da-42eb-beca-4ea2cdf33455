import { BaseApiObject } from '../typings';

export type DomainType = 'static' | 'dynamic';
export const DOMAIN_TYPES: { 'static': DomainType, 'dynamic': DomainType; } = {
  'static': 'static',
  'dynamic': 'dynamic'
};

export type StaticDomainType = 'static' | 'lobby' | 'live-streaming' | 'ehub';
export const STATIC_DOMAIN_TYPES: {
  static: StaticDomainType,
  lobby: StaticDomainType,
  liveStreaming: StaticDomainType,
  ehub: StaticDomainType;
} = {
  static: 'static',
  lobby: 'lobby',
  liveStreaming: 'live-streaming',
  ehub: 'ehub'
};

export type DomainWatcherAdapterType = 'tapking';
export const DOMAIN_WATCHER_ADAPTERS: { tapking: DomainWatcherAdapterType; } = {
  tapking: 'tapking'
};

export interface DomainPoolItem {
  id: string;
  isActive?: boolean;
}

export interface DomainPoolData {
  name?: string;
  domainWatcherAdapterId?: string;
  domains?: DomainPoolItem[];
}

export interface DomainPoolRow extends DomainPoolData {
  id: string;
  type: DomainType;
  inherited?: boolean;
  domainWatcherAdapterId?: string;
  createdAt: string;
  updatedAt: string;
}

export type DomainPool = DomainPoolRow & BaseApiObject;

export interface DomainRow {
  id: string;
  domain: string;
  environment: string;
  type?: StaticDomainType;
  description?: string;
  isActive?: boolean;
  inherited?: boolean;
  expiryDate?: string;
  createdAt: string;
  updatedAt: string;
}

export type Domain = DomainRow & BaseApiObject;

export interface DomainsItemDialogData {
  domain?: Domain;
  type: DomainType;
}
