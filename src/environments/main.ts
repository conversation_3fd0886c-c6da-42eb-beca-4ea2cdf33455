import { env } from './base';

export interface Environment {
  production: boolean;
  ENV_DEBUG: 'true' | 'false';
  APP_VERSION: string;
  ENV_APP_VERSION?: string;
  ENV_BASE_URL: string;
  ENV_API_SERVER_ENDPOINT: string;
  ENV_API_SERVER_ENDPOINT2: string;
  ENV_REFRESH_TOKEN_TIME: number;
  ENV_GRAYLOG_API_ENDPOINT: string;
}

function gitVersion({ build = 0, git: { revision = '', datetime = '', branch = '' } = {} }) {
  return `${revision} ${build} ${branch} ${datetime}`;
}

export const main: Environment = {
  production: false,
  ENV_DEBUG: env.DEBUG || 'true',
  APP_VERSION: `${env.version} ${gitVersion(env)}`,
  ENV_BASE_URL: env.BASE_URL || '/',
  ENV_API_SERVER_ENDPOINT: env.API_SERVER_ENDPOINT || '/v1',
  ENV_API_SERVER_ENDPOINT2: env.API_SERVER_ENDPOINT2 || '/v2',
  ENV_REFRESH_TOKEN_TIME: parseInt(env.REFRESH_TOKEN_TIME, 10) || 300000, // 5 min
  ENV_GRAYLOG_API_ENDPOINT: env.API_GRAYLOG_API_ENDPOINT || 'https://104.199.232.139:8138/gelf',
};
