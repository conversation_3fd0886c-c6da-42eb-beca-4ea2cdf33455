{"name": "@skywind-group/sw-management-gameprovider", "version": "2.141.0", "description": "Contains several interfaces to share between gameprovider and game auth service", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "compile": "tsc -b tsconfig.build.json", "version": "mkdir -p lib/skywind && echo $(node -p \"require('./package.json').version\") $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version"}, "dependencies": {"@skywind-group/sw-deferred-payment": "^2.0.0", "@skywind-group/sw-management-promo-wallet": "workspace:~2.141.0", "@skywind-group/sw-wallet-adapter-core": "2.1.9"}, "devDependencies": {"@skywind-group/sw-wallet": "1.0.8"}, "peerDependencies": {"uuid": "^9.0.1"}}