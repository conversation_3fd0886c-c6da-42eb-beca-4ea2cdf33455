import { lazy } from "@skywind-group/sw-utils";
import { DomainStatus, StaticDomainType } from "../../skywind/entities/domain";
import { FACTORY } from "./common";
import { Models } from "../../skywind/models/models";

const factory = require("factory-girl").factory;

interface DomainBuildOptions {
    domain?: string;
}

interface DynamicBuildOptions extends DomainBuildOptions {
    environment?: string;
}

interface StaticDomainPoolBuildOptions {
    name?: string;
    domainWatcherAdapterId?: string;
}

interface DynamicDomainPoolBuildOptions {
    name?: string;
    domainWatcherAdapterId?: string;
}

export const defineDomainFactory = lazy(() => {
    factory.define(FACTORY.STATIC_DOMAIN, Models.StaticDomainModel, (buildOptions: DomainBuildOptions) => {
        return {
            domain: buildOptions.domain || factory.chance("domain"),
            status: DomainStatus.ACTIVE,
            type: StaticDomainType.STATIC
        };
    });

    factory.define(FACTORY.DYNAMIC_DOMAIN, Models.DynamicDomainModel, (buildOptions: DynamicBuildOptions) => {
        return {
            domain: buildOptions.domain || factory.chance("domain"),
            environment: buildOptions.environment || factory.chance("word"),
            status: DomainStatus.ACTIVE
        };
    });

    factory.define(FACTORY.STATIC_DOMAIN_POOL, Models.StaticDomainPoolModel, (buildOptions: StaticDomainPoolBuildOptions) => {
        return {
            name: buildOptions.name || factory.chance("word"),
            domainWatcherAdapterId: buildOptions.domainWatcherAdapterId || null
        };
    });

    factory.define(FACTORY.DYNAMIC_DOMAIN_POOL, Models.DynamicDomainPoolModel, (buildOptions: DynamicDomainPoolBuildOptions) => {
        return {
            name: buildOptions.name || factory.chance("word"),
            domainWatcherAdapterId: buildOptions.domainWatcherAdapterId || null
        };
    });

    return factory;
});
