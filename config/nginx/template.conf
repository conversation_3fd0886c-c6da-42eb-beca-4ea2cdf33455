# Increase server names hash bucket size to handle longer server names
server_names_hash_bucket_size 128;

upstream upstream_api {
  server ${MANAGEMENT_API} fail_timeout=30s max_fails=100;
}

upstream upstream_softgate_api {
  server ${SOFTGATE_MANAGEMENT_API} fail_timeout=30s max_fails=100;
}

upstream upstream_integration_api {
  server ${INTEGRATION_API} fail_timeout=30s max_fails=100;
}

upstream upstream_lobby_build_api {
  server ${LOBBY_BUILD_API} fail_timeout=30s max_fails=100;
}

# Default/Fallback server block
server {
  listen 80 default_server;
  server_name _;

  include /usr/share/nginx/common.conf;

  location /favicon.ico {
    try_files /favicon.ico =404;
    access_log off;
    expires 1y;
  }

  location /api/config {
    default_type application/json;
    return 200 '{
      "host": "$host",
      "gosGameProviderCode": "${GOS_GAME_PROVIDER_CODE}",
      "liveChatLicence": "${LIVE_CHAT_LICENCE}",
      "csvMaxPlayersWithBalance": "${CSV_MAX_PLAYERS_WITH_BALANCE}",
      "csvMaxPlayersWithoutBalance": "${CSV_MAX_PLAYERS_WITHOUT_BALANCE}",
      "promotions": {
        "maxCsvFileSize": "${PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE}",
        "maxCsvLines": "${PROMO_PARTICIPANTS_MAX_CSV_ITEMS}"
      },
      "bridge": "${BRIDGE_URL}",
      "loginUrl": "${LOGIN_URL}",
      "hubs": {
        "casino": "${CASINO_HUB_URL}",
        "engagement": "${ENGAGEMENT_HUB_URL}",
        "analytics": "${DATA_HUB_URL}",
        "studio": "${STUDIO_HUB_URL}"
      },
      "envName": "${ENV_NAME}",
      "locationName": "${LOCATION_NAME}"
    }';
  }

  location /v1 {
    proxy_hide_header 'Access-Control-Allow-Origin';
    proxy_hide_header 'Access-Control-Allow-Methods';
    proxy_hide_header 'Access-Control-Allow-Headers';
    proxy_hide_header 'Access-Control-Allow-Credentials';
    proxy_hide_header 'Access-Control-Max-Age';

    if ($request_method = 'OPTIONS') {
      add_header 'Access-Control-Allow-Origin' $http_origin always;
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, DELETE, PATCH, PUT' always;
      add_header 'Access-Control-Allow-Headers' 'x-access-token, accept, content-type' always;
      add_header 'Access-Control-Allow-Credentials' 'true' always;
      add_header 'Access-Control-Max-Age' 2592000 always;
      return 204;
    }

    add_header 'Access-Control-Allow-Origin' $http_origin always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Allow-Headers' 'x-access-token, accept, content-type' always;

    proxy_pass http://upstream_api/v1;
  }

  location /v2 {
    proxy_pass http://upstream_api/v2;
  }

  location /api/v1/srt {
    proxy_pass http://upstream_api/auth-gateway/srt-api/srt;
  }

  location /auth-gateway {
    proxy_pass http://upstream_api/auth-gateway;
  }

  # Game provider
  location /gameprovider/v1 {
    proxy_pass http://upstream_api/gameprovider/v1;
  }

  location /gameprovider/v2 {
    proxy_pass http://upstream_api/gameprovider/v2;
  }

  # Players
  location /player/v1 {
    proxy_pass http://upstream_api/player/v1;
  }

  # site
  location /site/v1 {
    proxy_pass http://upstream_api/site/v1;
  }

  location /api/v1/integration {
      proxy_pass http://upstream_integration_api/v1;
  }

  location /api/v1/lobby-build/ {
      proxy_pass http://upstream_lobby_build_api/;
  }

  location /cdn/ {
      proxy_pass ${GAMES_URL}/;
  }

  location /widgets {
      proxy_pass ${LOBBY_WIDGETS_URL}/widgets;
  }
}

# Softgate virtual host
server {
  listen 80;
  server_name ${SOFTGATE_HOST};

  include /usr/share/nginx/common.conf;

  location /favicon.ico {
    try_files /img/softgate/favicon.ico /favicon.ico =404;
    access_log off;
    expires 1y;
  }

  location /api/config {
    default_type application/json;
    return 200 '{
      "host": "$host",
      "gosGameProviderCode": "${GOS_GAME_PROVIDER_CODE}",
      "liveChatLicence": "${LIVE_CHAT_LICENCE}",
      "csvMaxPlayersWithBalance": "${CSV_MAX_PLAYERS_WITH_BALANCE}",
      "csvMaxPlayersWithoutBalance": "${CSV_MAX_PLAYERS_WITHOUT_BALANCE}",
      "promotions": {
        "maxCsvFileSize": "${PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE}",
        "maxCsvLines": "${PROMO_PARTICIPANTS_MAX_CSV_ITEMS}"
      },
      "bridge": "${SOFTGATE_BRIDGE_URL}",
      "loginUrl": "${SOFTGATE_LOGIN_URL}",
      "hubs": {
        "casino": "${SOFTGATE_CASINO_HUB_URL}",
        "engagement": "${SOFTGATE_ENGAGEMENT_HUB_URL}",
        "analytics": "${SOFTGATE_DATA_HUB_URL}",
        "studio": "${SOFTGATE_STUDIO_HUB_URL}"
      },
      "logo": {
        "main": "/img/softgate/logo.png",
        "solo": "/img/softgate/logo.png",
        "white": ""
      },
      "envName": "${ENV_NAME}",
      "locationName": "${LOCATION_NAME}"
    }';
  }

  location /v1 {
    proxy_hide_header 'Access-Control-Allow-Origin';
    proxy_hide_header 'Access-Control-Allow-Methods';
    proxy_hide_header 'Access-Control-Allow-Headers';
    proxy_hide_header 'Access-Control-Allow-Credentials';
    proxy_hide_header 'Access-Control-Max-Age';

    if ($request_method = 'OPTIONS') {
      add_header 'Access-Control-Allow-Origin' $http_origin always;
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, DELETE, PATCH, PUT' always;
      add_header 'Access-Control-Allow-Headers' 'x-access-token, accept, content-type' always;
      add_header 'Access-Control-Allow-Credentials' 'true' always;
      add_header 'Access-Control-Max-Age' 2592000 always;
      return 204;
    }

    add_header 'Access-Control-Allow-Origin' $http_origin always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Allow-Headers' 'x-access-token, accept, content-type' always;

    proxy_pass http://upstream_softgate_api/v1;
  }

  location /v2 {
    proxy_pass http://upstream_softgate_api/v2;
  }

  location /api/v1/srt {
    proxy_pass http://upstream_softgate_api/auth-gateway/srt-api/srt;
  }

  location /auth-gateway {
    proxy_pass http://upstream_softgate_api/auth-gateway;
  }

  # Game provider
  location /gameprovider/v1 {
    proxy_pass http://upstream_softgate_api/gameprovider/v1;
  }

  location /gameprovider/v2 {
    proxy_pass http://upstream_softgate_api/gameprovider/v2;
  }

  # Players
  location /player/v1 {
    proxy_pass http://upstream_softgate_api/player/v1;
  }

  # site
  location /site/v1 {
    proxy_pass http://upstream_softgate_api/site/v1;
  }

  location /api/v1/integration {
      proxy_pass http://upstream_integration_api/v1;
  }

  location /api/v1/lobby-build/ {
      proxy_pass http://upstream_lobby_build_api/;
  }

  location /cdn/ {
      proxy_pass ${GAMES_URL}/;
  }

  location /widgets {
      proxy_pass ${LOBBY_WIDGETS_URL}/widgets;
  }
}
